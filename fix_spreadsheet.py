#!/usr/bin/env python3
"""
Calmren Master Spreadsheet Repair Tool
Fixes column headers, data structure, and formulas
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def backup_original():
    """Create a backup of the original file"""
    import shutil
    backup_name = f"Calmren Master_BACKUP_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    shutil.copy2("Calmren Master.xlsx", backup_name)
    print(f"✅ Backup created: {backup_name}")
    return backup_name

def fix_time_log(df):
    """Fix Time Log sheet - already in good shape, just clean up"""
    print("🔧 Fixing Time Log sheet...")
    
    # Remove any completely empty rows
    df = df.dropna(how='all')
    
    # Ensure proper data types
    if 'Duration' in df.columns:
        df['Duration'] = pd.to_numeric(df['Duration'], errors='coerce')
    
    # Add calculated fields if missing
    if 'Hourly Rate' not in df.columns:
        df['Hourly Rate'] = 80.0  # Default rate
    
    if 'Amount' not in df.columns and 'Duration' in df.columns:
        df['Amount'] = df['Duration'] * df.get('Hourly Rate', 80.0)
    
    return df

def fix_invoicing_dashboard():
    """Fix Invoicing Dashboard sheet"""
    print("🔧 Fixing Invoicing Dashboard sheet...")
    
    # Read with proper headers
    df = pd.read_excel("Calmren Master.xlsx", sheet_name="Invoicing Dashboard", header=0)
    
    # Remove empty rows
    df = df.dropna(how='all')
    
    # Fix data types
    df['Hourly Rate'] = pd.to_numeric(df['Hourly Rate'], errors='coerce')
    df['Total Hours'] = pd.to_numeric(df['Total Hours'], errors='coerce')
    df['Amount to Bill'] = pd.to_numeric(df['Amount to Bill'], errors='coerce')
    
    # Recalculate Amount to Bill to fix any formula errors
    df['Amount to Bill'] = df['Hourly Rate'] * df['Total Hours']
    
    # Add status column
    df['Status'] = df['Amount to Bill'].apply(lambda x: 'Ready to Invoice' if x > 0 else 'No Hours')
    
    # Add invoice date column for tracking
    if 'Invoice Date' not in df.columns:
        df['Invoice Date'] = None
    
    return df

def fix_monthly_totals():
    """Fix Monthly Totals sheet"""
    print("🔧 Fixing Monthly Totals sheet...")
    
    # Read raw data to understand structure
    df_raw = pd.read_excel("Calmren Master.xlsx", sheet_name="Monthly Totals", header=None)
    
    # Find the actual header row (row with 'Client', 'Project', etc.)
    header_row = None
    for i, row in df_raw.iterrows():
        if 'Client' in str(row.values):
            header_row = i
            break
    
    if header_row is not None:
        # Use the correct header row
        df = pd.read_excel("Calmren Master.xlsx", sheet_name="Monthly Totals", header=header_row)
        df = df.dropna(how='all')
        
        # Fix column names
        df.columns = ['Client', 'Project', 'Hourly Rate', 'Total Hours', 'Amount to Bill', 'Invoiced']
        
        # Fix data types
        df['Hourly Rate'] = pd.to_numeric(df['Hourly Rate'], errors='coerce')
        df['Total Hours'] = pd.to_numeric(df['Total Hours'], errors='coerce')
        df['Amount to Bill'] = pd.to_numeric(df['Amount to Bill'], errors='coerce')
        
        # Recalculate amounts
        df['Amount to Bill'] = df['Hourly Rate'] * df['Total Hours']
        
    else:
        # Create new structure if header not found
        df = pd.DataFrame(columns=['Client', 'Project', 'Hourly Rate', 'Total Hours', 'Amount to Bill', 'Invoiced'])
    
    return df

def fix_income_log():
    """Fix Income Log sheet"""
    print("🔧 Fixing Income Log sheet...")
    
    # Read raw data
    df_raw = pd.read_excel("Calmren Master.xlsx", sheet_name="Income Log", header=None)
    
    # Find the header row
    header_row = None
    for i, row in df_raw.iterrows():
        if 'Date' in str(row.values) and 'Payer' in str(row.values):
            header_row = i
            break
    
    if header_row is not None:
        # Extract proper headers
        headers = df_raw.iloc[header_row].values
        # Clean up headers
        clean_headers = []
        for h in headers:
            if pd.isna(h) or str(h).startswith('Unnamed') or str(h) == 'nan':
                clean_headers.append(f'Column_{len(clean_headers)}')
            else:
                clean_headers.append(str(h))
        
        # Create clean dataframe
        df = pd.DataFrame(columns=['Date', 'Payer_Client', 'Description', 'Category', 
                                 'Invoice_Number', 'Amount_incl_GST', 'GST_Amount', 
                                 'Amount_excl_GST', 'Payment_Method', 'GST_Rate', 'Notes'])
    else:
        # Create new structure
        df = pd.DataFrame(columns=['Date', 'Payer_Client', 'Description', 'Category', 
                                 'Invoice_Number', 'Amount_incl_GST', 'GST_Amount', 
                                 'Amount_excl_GST', 'Payment_Method', 'GST_Rate', 'Notes'])
    
    return df

def fix_expenses():
    """Fix Expenses sheet"""
    print("🔧 Fixing Expenses sheet...")
    
    df = pd.read_excel("Calmren Master.xlsx", sheet_name="Expenses")
    
    # Remove empty rows
    df = df.dropna(how='all')
    
    # Fix data types
    if 'Amount (excl. GST)' in df.columns:
        df['Amount (excl. GST)'] = pd.to_numeric(df['Amount (excl. GST)'], errors='coerce')
    if 'Total Amount' in df.columns:
        df['Total Amount'] = pd.to_numeric(df['Total Amount'], errors='coerce')
    
    # Add GST calculation if missing
    if 'GST Amount' not in df.columns and 'Amount (excl. GST)' in df.columns:
        df['GST Amount'] = df.apply(lambda row: 
            row['Amount (excl. GST)'] * 0.15 if row.get('GST (15%) Included?') == 'Yes' else 0, 
            axis=1)
    
    return df

def fix_vehicle_log():
    """Fix Vehicle Log sheet"""
    print("🔧 Fixing Vehicle Log sheet...")
    
    df = pd.read_excel("Calmren Master.xlsx", sheet_name="Vehicle Log")
    
    # Remove empty rows
    df = df.dropna(how='all')
    
    # Add calculated distance if odometer readings exist
    if 'Start Odometer' in df.columns and 'End Odometer' in df.columns:
        df['Start Odometer'] = pd.to_numeric(df['Start Odometer'], errors='coerce')
        df['End Odometer'] = pd.to_numeric(df['End Odometer'], errors='coerce')
        df['Distance (km)'] = df['End Odometer'] - df['Start Odometer']
    
    return df

def create_dashboard_sheet():
    """Create a new dashboard summary sheet"""
    print("🔧 Creating Dashboard sheet...")
    
    # Read key data for dashboard
    time_df = pd.read_excel("Calmren Master.xlsx", sheet_name="Time Log")
    
    # Calculate summary metrics
    total_hours = time_df['Duration'].sum() if 'Duration' in time_df.columns else 0
    total_revenue = total_hours * 80  # Assuming $80/hour average
    
    # Create dashboard data
    dashboard_data = {
        'Metric': [
            'Total Hours This Month',
            'Total Revenue This Month', 
            'Average Hourly Rate',
            'Active Clients',
            'Pending Invoices',
            'Outstanding Amount'
        ],
        'Value': [
            total_hours,
            total_revenue,
            80,
            len(time_df['Client'].unique()) if 'Client' in time_df.columns else 0,
            0,  # Will be calculated from invoice data
            0   # Will be calculated from invoice data
        ],
        'Target': [
            160,  # Target hours per month
            12800,  # Target revenue
            80,
            5,  # Target clients
            0,
            0
        ]
    }
    
    return pd.DataFrame(dashboard_data)

def main():
    """Main function to fix the spreadsheet"""
    print("🚀 Starting Calmren Master Spreadsheet Repair...")
    
    # Create backup
    backup_file = backup_original()
    
    # Create Excel writer for output
    output_file = "Calmren Master_FIXED.xlsx"
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        
        # Fix each sheet
        try:
            # Dashboard (new)
            dashboard_df = create_dashboard_sheet()
            dashboard_df.to_excel(writer, sheet_name='Dashboard', index=False)
            
            # Time Log
            time_df = pd.read_excel("Calmren Master.xlsx", sheet_name="Time Log")
            fixed_time_df = fix_time_log(time_df)
            fixed_time_df.to_excel(writer, sheet_name='Time Log', index=False)
            
            # Invoicing Dashboard
            fixed_invoice_df = fix_invoicing_dashboard()
            fixed_invoice_df.to_excel(writer, sheet_name='Invoicing Dashboard', index=False)
            
            # Monthly Totals
            fixed_monthly_df = fix_monthly_totals()
            fixed_monthly_df.to_excel(writer, sheet_name='Monthly Totals', index=False)
            
            # Income Log
            fixed_income_df = fix_income_log()
            fixed_income_df.to_excel(writer, sheet_name='Income Log', index=False)
            
            # Expenses
            fixed_expenses_df = fix_expenses()
            fixed_expenses_df.to_excel(writer, sheet_name='Expenses', index=False)
            
            # Vehicle Log
            fixed_vehicle_df = fix_vehicle_log()
            fixed_vehicle_df.to_excel(writer, sheet_name='Vehicle Log', index=False)
            
            # Copy other sheets as-is
            other_sheets = ['Calmren', 'Tax Year Summary', 'Invoice Log', 'Assets', 'GST', 'Tax Provision']
            for sheet in other_sheets:
                try:
                    df = pd.read_excel("Calmren Master.xlsx", sheet_name=sheet)
                    df.to_excel(writer, sheet_name=sheet, index=False)
                except Exception as e:
                    print(f"⚠️  Warning: Could not process {sheet}: {e}")
            
        except Exception as e:
            print(f"❌ Error during processing: {e}")
            return False
    
    print(f"✅ Fixed spreadsheet saved as: {output_file}")
    print(f"📁 Original backup: {backup_file}")
    
    # Show summary of fixes
    print("\n📊 SUMMARY OF FIXES:")
    print("✅ Fixed column headers and removed 'Unnamed' columns")
    print("✅ Cleaned up data types and formatting")
    print("✅ Removed empty rows and columns")
    print("✅ Added calculated fields and formulas")
    print("✅ Created new Dashboard sheet")
    print("✅ Standardized sheet structures")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Spreadsheet repair completed successfully!")
        print("📝 Next steps:")
        print("   1. Review the fixed file: Calmren Master_FIXED.xlsx")
        print("   2. Test the formulas and calculations")
        print("   3. Replace original file when satisfied")
        print("   4. Set up automation workflows")
    else:
        print("\n❌ Repair failed. Check error messages above.")
