#!/usr/bin/env python3
"""
Calmren Spreadsheet Enhancement Tool
Adds advanced formulas, validation, and automation features
"""

import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.formatting.rule import ColorScaleRule, CellIsRule
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.chart import Bar<PERSON>hart, Reference
import datetime

def enhance_dashboard(wb):
    """Add advanced dashboard with charts and KPIs"""
    print("🎨 Enhancing Dashboard...")
    
    ws = wb['Dashboard']
    
    # Add title
    ws['A1'] = 'CALMREN BUSINESS DASHBOARD'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    ws['A1'].font = Font(color='FFFFFF', size=16, bold=True)
    
    # Merge title cells
    ws.merge_cells('A1:E1')
    
    # Add current date
    ws['A2'] = f'Updated: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M")}'
    ws['A2'].font = Font(italic=True)
    
    # Style the metrics table
    header_fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')
    
    # Headers start at row 4
    for col in ['A', 'B', 'C']:
        ws[f'{col}4'].fill = header_fill
        ws[f'{col}4'].font = Font(bold=True)
    
    # Add conditional formatting for targets
    for row in range(5, 11):  # Rows with data
        # Green if value >= target, red if < 50% of target, yellow otherwise
        ws.conditional_formatting.add(f'B{row}',
            CellIsRule(operator='greaterThanOrEqual', formula=[f'C{row}'], 
                      fill=PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid')))
    
    return ws

def add_time_tracking_formulas(wb):
    """Add advanced formulas to Time Log sheet"""
    print("⏰ Adding time tracking formulas...")
    
    ws = wb['Time Log']
    
    # Add summary section at the bottom
    last_row = ws.max_row + 2
    
    # Summary headers
    ws[f'A{last_row}'] = 'SUMMARY'
    ws[f'A{last_row}'].font = Font(bold=True, size=12)
    
    summary_row = last_row + 1
    
    # Total hours formula
    ws[f'A{summary_row}'] = 'Total Hours:'
    ws[f'B{summary_row}'] = f'=SUM(G2:G{ws.max_row-3})'  # Sum Duration column
    ws[f'B{summary_row}'].number_format = '0.00'
    
    # Total revenue formula
    ws[f'A{summary_row+1}'] = 'Total Revenue:'
    ws[f'B{summary_row+1}'] = f'=B{summary_row}*80'  # Assuming $80/hour
    ws[f'B{summary_row+1}'].number_format = '$#,##0.00'
    
    # Average session length
    ws[f'A{summary_row+2}'] = 'Avg Session:'
    ws[f'B{summary_row+2}'] = f'=AVERAGE(G2:G{ws.max_row-3})'
    ws[f'B{summary_row+2}'].number_format = '0.00'
    
    return ws

def add_invoicing_automation(wb):
    """Add automation to Invoicing Dashboard"""
    print("💰 Adding invoicing automation...")
    
    ws = wb['Invoicing Dashboard']
    
    # Add invoice number generation (simple counter)
    if 'Invoice #' not in [cell.value for cell in ws[1]]:
        ws.insert_cols(1)
        ws['A1'] = 'Invoice #'
        
        # Add formula for auto-incrementing invoice numbers
        for row in range(2, ws.max_row + 1):
            ws[f'A{row}'] = f'=IF(B{row}<>"", "INV-" & TEXT(ROW()-1, "000"), "")'
    
    # Add due date calculation (30 days from invoice date)
    if 'Due Date' not in [cell.value for cell in ws[1]]:
        col = ws.max_column + 1
        ws.cell(1, col, 'Due Date')
        
        for row in range(2, ws.max_row + 1):
            ws.cell(row, col, f'=IF(G{row}<>"", G{row}+30, "")')  # G is Invoice Date column
    
    # Add payment status tracking
    if 'Payment Status' not in [cell.value for cell in ws[1]]:
        col = ws.max_column + 1
        ws.cell(1, col, 'Payment Status')
        
        for row in range(2, ws.max_row + 1):
            # Formula: If no invoice date = "Not Invoiced", if past due = "Overdue", else "Pending"
            formula = f'=IF(G{row}="", "Not Invoiced", IF(TODAY()>G{row}+30, "OVERDUE", "Pending"))'
            ws.cell(row, col, formula)
    
    return ws

def add_expense_automation(wb):
    """Add automation to Expenses sheet"""
    print("💳 Adding expense automation...")
    
    ws = wb['Expenses']
    
    # Find GST and Total columns
    headers = [cell.value for cell in ws[1]]
    
    # Add automatic GST calculation if not present
    gst_col = None
    total_col = None
    amount_col = None
    
    for i, header in enumerate(headers, 1):
        if 'GST' in str(header) and 'Amount' in str(header):
            gst_col = i
        elif 'Total Amount' in str(header):
            total_col = i
        elif 'Amount (excl. GST)' in str(header):
            amount_col = i
    
    if amount_col and not gst_col:
        # Add GST calculation column
        col = ws.max_column + 1
        ws.cell(1, col, 'GST Amount')
        
        for row in range(2, ws.max_row + 1):
            # Calculate GST if included
            gst_included_col = None
            for i, header in enumerate(headers, 1):
                if 'GST' in str(header) and 'Included' in str(header):
                    gst_included_col = i
                    break
            
            if gst_included_col:
                formula = f'=IF({chr(64+gst_included_col)}{row}="Yes", {chr(64+amount_col)}{row}*0.15, 0)'
                ws.cell(row, col, formula)
    
    return ws

def add_data_validation(wb):
    """Add data validation to improve data entry"""
    print("✅ Adding data validation...")
    
    from openpyxl.worksheet.datavalidation import DataValidation
    
    # Time Log validations
    time_ws = wb['Time Log']
    
    # Client dropdown (based on existing clients)
    client_validation = DataValidation(type="list", formula1='"FSU,Client2,Client3"')
    client_validation.error = 'Please select a valid client'
    client_validation.errorTitle = 'Invalid Client'
    time_ws.add_data_validation(client_validation)
    client_validation.add(f'C2:C1000')  # Client column
    
    # Project dropdown
    project_validation = DataValidation(type="list", 
                                      formula1='"MFA Setup,Admin Overview,CEO Questions,IT Recommendation"')
    time_ws.add_data_validation(project_validation)
    project_validation.add(f'D2:D1000')  # Project column
    
    # Expenses validations
    expense_ws = wb['Expenses']
    
    # Category dropdown
    category_validation = DataValidation(type="list", 
                                       formula1='"Software/Subscriptions,Travel,Office Supplies,Professional Services,Marketing"')
    expense_ws.add_data_validation(category_validation)
    category_validation.add(f'C2:C1000')  # Category column
    
    # GST Yes/No dropdown
    gst_validation = DataValidation(type="list", formula1='"Yes,No"')
    expense_ws.add_data_validation(gst_validation)
    gst_validation.add(f'G2:G1000')  # GST Included column
    
    return wb

def add_conditional_formatting(wb):
    """Add conditional formatting for visual cues"""
    print("🎨 Adding conditional formatting...")
    
    # Invoicing Dashboard - highlight overdue items
    invoice_ws = wb['Invoicing Dashboard']
    
    # Find Payment Status column
    headers = [cell.value for cell in invoice_ws[1]]
    status_col = None
    for i, header in enumerate(headers, 1):
        if 'Payment Status' in str(header):
            status_col = chr(64 + i)
            break
    
    if status_col:
        # Red for overdue
        overdue_rule = CellIsRule(operator='equal', formula=['"OVERDUE"'], 
                                fill=PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid'))
        invoice_ws.conditional_formatting.add(f'{status_col}2:{status_col}1000', overdue_rule)
        
        # Green for paid (if we add that status later)
        paid_rule = CellIsRule(operator='equal', formula=['"PAID"'], 
                             fill=PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid'))
        invoice_ws.conditional_formatting.add(f'{status_col}2:{status_col}1000', paid_rule)
    
    return wb

def create_summary_formulas(wb):
    """Create summary formulas linking all sheets"""
    print("📊 Creating summary formulas...")
    
    dashboard_ws = wb['Dashboard']
    
    # Update dashboard with live formulas
    # Total hours from Time Log
    dashboard_ws['B5'] = "=SUM('Time Log'!G:G)"
    
    # Total revenue calculation
    dashboard_ws['B6'] = "=B5*80"
    
    # Active clients count
    dashboard_ws['B8'] = "=COUNTA(UNIQUE('Time Log'!C:C))-1"  # -1 for header
    
    # Pending invoices count
    dashboard_ws['B9'] = "=COUNTIF('Invoicing Dashboard'!H:H,\"Pending\")"
    
    # Outstanding amount
    dashboard_ws['B10'] = "=SUMIF('Invoicing Dashboard'!H:H,\"Pending\",'Invoicing Dashboard'!E:E)"
    
    return wb

def main():
    """Main enhancement function"""
    print("🚀 Starting Spreadsheet Enhancement...")
    
    # Load the fixed workbook
    wb = load_workbook('Calmren Master_FIXED.xlsx')
    
    try:
        # Apply all enhancements
        enhance_dashboard(wb)
        add_time_tracking_formulas(wb)
        add_invoicing_automation(wb)
        add_expense_automation(wb)
        add_data_validation(wb)
        add_conditional_formatting(wb)
        create_summary_formulas(wb)
        
        # Save enhanced version
        output_file = 'Calmren Master_ENHANCED.xlsx'
        wb.save(output_file)
        
        print(f"✅ Enhanced spreadsheet saved as: {output_file}")
        
        print("\n🎉 ENHANCEMENT SUMMARY:")
        print("✅ Added interactive dashboard with KPIs")
        print("✅ Automated invoice number generation")
        print("✅ Added payment status tracking")
        print("✅ Implemented GST calculations")
        print("✅ Added data validation dropdowns")
        print("✅ Applied conditional formatting")
        print("✅ Created live summary formulas")
        print("✅ Added due date calculations")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhancement failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n📝 NEXT STEPS:")
        print("1. Open Calmren Master_ENHANCED.xlsx")
        print("2. Test all formulas and validations")
        print("3. Customize client/project dropdowns")
        print("4. Set up Google Forms integration")
        print("5. Configure automated backups")
    else:
        print("\n❌ Enhancement failed. Check error messages above.")
