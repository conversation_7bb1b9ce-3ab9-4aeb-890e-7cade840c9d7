{"spreadsheet_name": "<PERSON><PERSON><PERSON> - Google Sheets", "sheets": {"Dashboard": {"purpose": "Executive summary with live KPIs", "formulas": {"A1": "CALMREN BUSINESS DASHBOARD", "A2": "Last Updated: 2025-09-13 11:33", "A4": "Metric", "B4": "Current", "C4": "Target", "A5": "This Month Hours", "B5": "=SUMIFS(TimeLog!G:G,TimeLog!B:B,\">=\"&DATE(YEAR(TODAY()),MONTH(TODAY()),1),TimeLog!B:B,\"<\"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))", "C5": "160", "A6": "This Month Revenue", "B6": "=SUMIFS(TimeLog!I:I,TimeLog!B:B,\">=\"&DATE(YEAR(TODAY()),MONTH(TODAY()),1),TimeLog!B:B,\"<\"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))", "C6": "12800", "A7": "Active Clients", "B7": "=COUNTA(UNIQUE(FILTER(TimeLog!C:C,TimeLog!B:B>=DATE(YEAR(TODAY()),MONTH(TODAY()),1))))", "C7": "5", "A8": "Average Rate", "B8": "=AVERAGE(FILTER(TimeLog!H:H,TimeLog!H:H>0))", "C8": "80", "A9": "Pending Invoices", "B9": "=COUNTIF(InvoicingDashboard!H:H,\"Pending\")", "C9": "0", "A10": "Outstanding Amount", "B10": "=SUMIF(InvoicingDashboard!H:H,\"Pending\",InvoicingDashboard!E:E)", "C10": "0"}, "features": ["Real-time metrics from all sheets", "Visual progress indicators", "Monthly targets tracking", "Last updated timestamp"]}, "TimeLog": {"purpose": "Time tracking with Google Form integration", "formulas": {"A1": "Timestamp", "B1": "Date", "C1": "Client", "D1": "Project", "E1": "Start Time", "F1": "End Time", "G1": "Duration (Hours)", "H1": "Hourly Rate", "I1": "Amount", "J1": "Task Description", "K1": "Status", "G2": "=IF(AND(E2<>\"\",F2<>\"\"),IF(F2>E2,(F2-E2)*24,(F2+1-E2)*24),\"\")", "H2": "80", "I2": "=IF(AND(G2<>\"\",H2<>\"\"),G2*H2,\"\")", "K2": "=IF(I2<>\"\",\"Logged\",\"\")"}, "features": ["Direct Google Form input", "Automatic duration calculation", "Amount calculation based on hourly rate", "Status tracking"]}, "InvoicingDashboard": {"purpose": "Invoice generation and tracking", "formulas": {"A1": "Invoice #", "B1": "Client", "C1": "Project", "D1": "Total Hours", "E1": "Amount to Bill", "F1": "Hourly Rate", "G1": "Invoice Date", "H1": "Payment Status", "I1": "Due Date", "A2": "=IF(B2<>\"\",\"INV-\"&TEXT(ROW()-1,\"000\"),\"\")", "D2": "=SUMIFS(TimeLog!G:G,TimeLog!C:C,B2,TimeLog!D:D,C2)", "E2": "=D2*F2", "F2": "80", "H2": "=IF(G2=\"\",\"Not Invoiced\",IF(TODAY()>I2,\"OVERDUE\",\"Pending\"))", "I2": "=IF(G2<>\"\",G2+30,\"\")"}, "features": ["Auto invoice numbering", "Payment status tracking", "Due date calculation", "Overdue highlighting"]}, "MonthlyTotals": {"purpose": "Automated monthly summaries", "formulas": {"A1": "Month", "B1": "Client", "C1": "Project", "D1": "Hours", "E1": "Revenue", "F1": "Avg Rate", "A3": "=QUERY(TimeLog!B:I,\"SELECT MONTH(B), C, D, SUM(G), SUM(I), AVG(H) WHERE B IS NOT NULL GROUP BY MONTH(B), C, D ORDER BY MONTH(B) DESC\",1)"}, "features": ["QUERY-based auto-population", "Client and project breakdown", "Monthly trend analysis"]}, "Expenses": {"purpose": "Expense tracking with GST automation", "formulas": {"A1": "Date", "B1": "Category", "C1": "Supplier", "D1": "Description", "E1": "Amount (excl GST)", "F1": "GST Included?", "G1": "GST Amount", "H1": "Total Amount", "I1": "Receipt Link", "J1": "Payment Method", "G2": "=IF(F2=\"Yes\",E2*0.15,0)", "H2": "=E2+G2"}, "features": ["Automatic GST calculation", "Category validation", "Receipt link storage"]}}, "automation": {"apps_script": "Handles form submissions and calculations", "triggers": ["onFormSubmit - Process new time entries", "Monthly summary - Generate reports", "Dashboard updates - Real-time metrics"]}}