#!/usr/bin/env python3
"""
Calmren Monthly Statement Generator
Generates professional monthly statements summarising all invoices
"""

import pandas as pd
import datetime
from datetime import timed<PERSON>ta
import os
from pathlib import Path
import calendar

# Your business details
BUSINESS_INFO = {
    'name': '<PERSON><PERSON>',
    'email': '<EMAIL>',
    'address': '418a Kaitemako Rd',
    'city': 'Welcome Bay, 3175',
    'phone': '**********',
    'hourly_rate': 80.00,
    'bank_account': '04-2021-0224109-11'
}

# Client details
CLIENT_INFO = {
    'name': 'Free Speech Union (NZ) Incorporated',
    'website': 'fsu.nz'
}

def parse_date(date_str):
    """Parse date string in DD/MM/YYYY format"""
    try:
        return datetime.datetime.strptime(date_str, '%d/%m/%Y').date()
    except:
        return None

def load_time_data():
    """Load and process time log data"""
    print("📊 Loading time log data...")
    
    # Read CSV manually to handle commas in task descriptions
    import csv
    data = []
    
    with open('Calmren - Time Log.csv', 'r', encoding='utf-8') as file:
        lines = file.readlines()
        
        # Process header
        header = lines[0].strip().split(',')
        header = [col.strip() for col in header]
        
        # Process each data line
        for line_num, line in enumerate(lines[1:], 2):
            line = line.strip()
            if not line:
                continue
                
            # Split by comma, but rejoin the last parts as task description
            parts = line.split(',')
            if len(parts) >= 8:
                # First 7 fields are structured, rest is task description
                row = parts[:7] + [','.join(parts[7:])]
                data.append(row)
            else:
                print(f"⚠️  Skipping malformed line {line_num}: {line}")
    
    # Create DataFrame
    df = pd.DataFrame(data, columns=header)
    
    # Parse dates
    df['parsed_date'] = df['Date'].apply(parse_date)
    df = df.dropna(subset=['parsed_date'])
    
    # Convert duration to float
    df['Duration'] = pd.to_numeric(df['Duration'], errors='coerce')
    
    print(f"✅ Loaded {len(df)} time entries")
    return df

def get_monthly_data(df):
    """Group data by months"""
    months = {}
    
    for _, row in df.iterrows():
        date = row['parsed_date']
        month_key = date.strftime('%Y-%m')
        month_name = date.strftime('%B %Y')
        
        if month_key not in months:
            months[month_key] = {
                'month_name': month_name,
                'start_date': date.replace(day=1),
                'end_date': date.replace(day=calendar.monthrange(date.year, date.month)[1]),
                'entries': [],
                'total_hours': 0,
                'projects': {},
                'weeks': {}
            }
        
        months[month_key]['entries'].append(row)
        months[month_key]['total_hours'] += row['Duration']
        
        # Group by project
        project = row['Project']
        if project not in months[month_key]['projects']:
            months[month_key]['projects'][project] = {
                'hours': 0,
                'amount': 0
            }
        
        months[month_key]['projects'][project]['hours'] += row['Duration']
        months[month_key]['projects'][project]['amount'] += row['Duration'] * BUSINESS_INFO['hourly_rate']
        
        # Group by week for invoice references
        week_start = date - timedelta(days=date.weekday())
        week_key = week_start.strftime('%Y-%m-%d')
        
        if week_key not in months[month_key]['weeks']:
            months[month_key]['weeks'][week_key] = {
                'week_start': week_start,
                'week_end': week_start + timedelta(days=6),
                'hours': 0,
                'amount': 0
            }
        
        months[month_key]['weeks'][week_key]['hours'] += row['Duration']
        months[month_key]['weeks'][week_key]['amount'] += row['Duration'] * BUSINESS_INFO['hourly_rate']
    
    return months

def generate_statement_html(month_data, month_key, statement_number):
    """Generate monthly statement HTML"""
    
    month_name = month_data['month_name']
    start_date = month_data['start_date']
    end_date = month_data['end_date']
    total_hours = month_data['total_hours']
    total_amount = total_hours * BUSINESS_INFO['hourly_rate']
    
    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Statement {statement_number}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.4;
            color: #333;
        }}
        
        .statement-header {{
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
        }}
        
        .statement-title {{
            font-size: 2.5em;
            font-weight: bold;
            color: #2c5aa0;
            margin: 0;
        }}
        
        .statement-details {{
            text-align: right;
            font-size: 0.9em;
        }}
        
        .business-details {{
            margin-bottom: 30px;
        }}
        
        .business-name {{
            font-size: 1.4em;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
        }}
        
        .client-section {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
        }}
        
        .section-title {{
            font-size: 1.1em;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
        }}
        
        .summary-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        
        .summary-table th {{
            background-color: #2c5aa0;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
        }}
        
        .summary-table td {{
            padding: 12px;
            border-bottom: 1px solid #ddd;
        }}
        
        .summary-table tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        
        .total-row {{
            background-color: #e3f2fd !important;
            font-weight: bold;
            border-top: 2px solid #2c5aa0;
        }}
        
        .amount-cell {{
            text-align: right;
            font-family: 'Courier New', monospace;
        }}
        
        .total-amount {{
            font-size: 1.2em;
            color: #2c5aa0;
        }}
        
        .highlight-box {{
            background-color: #e8f4f8;
            border: 2px solid #2c5aa0;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }}
        
        .stat-card {{
            background: white;
            border: 2px solid #2c5aa0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        
        .stat-value {{
            font-size: 2.5em;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
        }}
        
        .stat-label {{
            font-size: 1em;
            color: #666;
        }}
        
        .footer {{
            margin-top: 40px;
            text-align: center;
            font-size: 0.9em;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }}
        
        @media print {{
            body {{ margin: 0; }}
            .statement-header {{ page-break-inside: avoid; }}
        }}
    </style>
</head>
<body>
    <div class="statement-header">
        <div>
            <h1 class="statement-title">MONTHLY STATEMENT</h1>
        </div>
        <div class="statement-details">
            <strong>Statement #:</strong> {statement_number}<br>
            <strong>Date:</strong> {datetime.date.today().strftime('%d %B %Y')}<br>
            <strong>Period:</strong> {month_name}
        </div>
    </div>

    <div class="business-details">
        <div class="business-name">{BUSINESS_INFO['name']}</div>
        <div>{BUSINESS_INFO['address']}</div>
        <div>{BUSINESS_INFO['city']}</div>
        <div>Email: {BUSINESS_INFO['email']}</div>
        <div>Phone: {BUSINESS_INFO['phone']}</div>
    </div>

    <div class="client-section">
        <div class="section-title">Statement For:</div>
        <div><strong>{CLIENT_INFO['name']}</strong></div>
        <div>{CLIENT_INFO['website']}</div>
    </div>

    <div class="highlight-box">
        <strong>Statement Period:</strong> {start_date.strftime('%d %B %Y')} – {end_date.strftime('%d %B %Y')}<br>
        <strong>Total Hours:</strong> {total_hours:.1f} hours<br>
        <strong>Total Amount:</strong> ${total_amount:.2f} (GST Exempt)
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{total_hours:.1f}</div>
            <div class="stat-label">Total Hours</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{len(month_data['projects'])}</div>
            <div class="stat-label">Active Projects</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{len(month_data['weeks'])}</div>
            <div class="stat-label">Billing Weeks</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${total_amount:.0f}</div>
            <div class="stat-label">Total Value</div>
        </div>
    </div>

    <table class="summary-table">
        <thead>
            <tr>
                <th>Project</th>
                <th style="width: 100px;">Hours</th>
                <th style="width: 100px;">Rate</th>
                <th style="width: 120px;">Amount</th>
            </tr>
        </thead>
        <tbody>"""
    
    # Add project rows
    for project, data in month_data['projects'].items():
        html += f"""
            <tr>
                <td>{project}</td>
                <td class="amount-cell">{data['hours']:.1f}</td>
                <td class="amount-cell">${BUSINESS_INFO['hourly_rate']:.2f}</td>
                <td class="amount-cell">${data['amount']:.2f}</td>
            </tr>"""
    
    html += f"""
            <tr class="total-row">
                <td colspan="3" style="text-align: right;"><strong>TOTAL (GST Exempt):</strong></td>
                <td class="amount-cell total-amount"><strong>${total_amount:.2f}</strong></td>
            </tr>
        </tbody>
    </table>

    <div class="section-title">Weekly Breakdown</div>
    <table class="summary-table">
        <thead>
            <tr>
                <th>Week Ending</th>
                <th style="width: 100px;">Hours</th>
                <th style="width: 120px;">Amount</th>
                <th style="width: 120px;">Invoice Ref</th>
            </tr>
        </thead>
        <tbody>"""
    
    # Add weekly rows
    invoice_counter = 1
    for week_key in sorted(month_data['weeks'].keys()):
        week_data = month_data['weeks'][week_key]
        week_end = week_data['week_end']
        year = week_end.year
        invoice_ref = f"INV-{year}-{invoice_counter:03d}"
        
        html += f"""
            <tr>
                <td>{week_end.strftime('%d %B %Y')}</td>
                <td class="amount-cell">{week_data['hours']:.1f}</td>
                <td class="amount-cell">${week_data['amount']:.2f}</td>
                <td class="amount-cell">{invoice_ref}</td>
            </tr>"""
        invoice_counter += 1
    
    html += f"""
        </tbody>
    </table>

    <div class="highlight-box">
        <strong>Payment Summary:</strong><br>
        Account Name: {BUSINESS_INFO['name']}<br>
        Account Number: {BUSINESS_INFO['bank_account']}<br>
        <strong>Total Amount Due:</strong> ${total_amount:.2f}
    </div>

    <div class="footer">
        <p><strong>Statement prepared:</strong> {datetime.date.today().strftime('%d %B %Y')}</p>
        <p>Thank you for your business! This statement summarises all services provided during {month_name}.</p>
        <p><em>Professional IT Services • Reliable • Efficient</em></p>
    </div>
</body>
</html>"""
    
    return html

def main():
    """Generate monthly statements"""
    print("🚀 Calmren Statement Generator Starting...")
    
    # Load data
    df = load_time_data()
    
    # Group by months
    months = get_monthly_data(df)
    
    print(f"📅 Found {len(months)} months of data")
    
    # Create output directory
    output_dir = Path("statements")
    output_dir.mkdir(exist_ok=True)
    
    # Generate statements for each month
    statement_counter = 1
    
    for month_key in sorted(months.keys()):
        month_data = months[month_key]
        
        # Generate statement number
        year, month = month_key.split('-')
        statement_number = f"STMT-{year}-{month}"
        
        print(f"📄 Generating statement {statement_number} for {month_data['month_name']}")
        
        # Generate statement HTML
        statement_html = generate_statement_html(month_data, month_key, statement_number)
        statement_file = output_dir / f"{statement_number}_Statement.html"
        
        with open(statement_file, 'w', encoding='utf-8') as f:
            f.write(statement_html)
        
        print(f"   ✅ Created: {statement_file.name}")
        
        statement_counter += 1
    
    print(f"\n🎉 Generated {len(months)} statements in '{output_dir}' directory")
    print("\n📧 Statement features:")
    print("   • Monthly summary of all work")
    print("   • Project breakdown by hours and amount")
    print("   • Weekly breakdown with invoice references")
    print("   • Professional formatting for client records")

if __name__ == "__main__":
    main()
