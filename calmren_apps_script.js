
/**
 * Calmren Business Automation Scripts
 * Auto-calculates time entries and manages invoicing
 */

// Trigger when form is submitted
function onFormSubmit(e) {
  console.log('Form submitted, processing...');
  
  var sheet = SpreadsheetApp.getActiveSheet();
  var lastRow = sheet.getLastRow();
  
  // Calculate duration if start and end times are provided
  calculateDuration(sheet, lastRow);
  
  // Calculate amount
  calculateAmount(sheet, lastRow);
  
  // Update dashboard
  updateDashboard();
  
  // Send notification (optional)
  // sendTimeEntryNotification(sheet, lastRow);
}

function calculateDuration(sheet, row) {
  try {
    var startTime = sheet.getRange(row, 5).getValue(); // Column E
    var endTime = sheet.getRange(row, 6).getValue();   // Column F
    
    if (startTime && endTime) {
      var duration;
      if (endTime > startTime) {
        duration = (endTime - startTime) / (1000 * 60 * 60); // Hours
      } else {
        // Handle overnight work (end time next day)
        duration = ((endTime + 24*60*60*1000) - startTime) / (1000 * 60 * 60);
      }
      
      sheet.getRange(row, 7).setValue(duration); // Column G
      console.log('Duration calculated: ' + duration + ' hours');
    }
  } catch (error) {
    console.error('Error calculating duration: ' + error);
  }
}

function calculateAmount(sheet, row) {
  try {
    var duration = sheet.getRange(row, 7).getValue(); // Column G
    var hourlyRate = sheet.getRange(row, 8).getValue() || 80; // Column H, default $80
    
    if (duration && hourlyRate) {
      var amount = duration * hourlyRate;
      sheet.getRange(row, 9).setValue(amount); // Column I
      sheet.getRange(row, 11).setValue('Logged'); // Column K - Status
      console.log('Amount calculated: $' + amount);
    }
  } catch (error) {
    console.error('Error calculating amount: ' + error);
  }
}

function updateDashboard() {
  try {
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var dashboard = ss.getSheetByName('Dashboard');
    
    // Update last updated timestamp
    dashboard.getRange('B2').setValue('Last Updated: ' + new Date().toLocaleString());
    
    console.log('Dashboard updated');
  } catch (error) {
    console.error('Error updating dashboard: ' + error);
  }
}

// Generate invoice for specific client/project
function generateInvoice(clientName, projectName) {
  try {
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var timeSheet = ss.getSheetByName('TimeLog');
    var invoiceSheet = ss.getSheetByName('InvoicingDashboard');
    
    // Get all time entries for this client/project
    var timeData = timeSheet.getDataRange().getValues();
    var totalHours = 0;
    var totalAmount = 0;
    var hourlyRate = 80; // Default
    
    for (var i = 1; i < timeData.length; i++) {
      if (timeData[i][2] === clientName && timeData[i][3] === projectName) {
        totalHours += timeData[i][6] || 0; // Duration
        totalAmount += timeData[i][8] || 0; // Amount
        if (timeData[i][7]) hourlyRate = timeData[i][7]; // Get actual rate
      }
    }
    
    if (totalHours > 0) {
      // Add to invoice sheet
      var lastRow = invoiceSheet.getLastRow() + 1;
      var invoiceNumber = 'INV-' + String(lastRow - 1).padStart(3, '0');
      
      invoiceSheet.getRange(lastRow, 1, 1, 9).setValues([[
        invoiceNumber,
        clientName,
        projectName,
        totalHours,
        totalAmount,
        hourlyRate,
        new Date(),
        'Pending',
        new Date(Date.now() + 30*24*60*60*1000) // Due in 30 days
      ]]);
      
      console.log('Invoice generated: ' + invoiceNumber);
      return invoiceNumber;
    }
  } catch (error) {
    console.error('Error generating invoice: ' + error);
  }
}

// Send email notification for new time entry
function sendTimeEntryNotification(sheet, row) {
  try {
    var data = sheet.getRange(row, 1, 1, 11).getValues()[0];
    var client = data[2];
    var project = data[3];
    var duration = data[6];
    var amount = data[8];
    
    var subject = 'New Time Entry Logged - ' + client;
    var body = `
New time entry recorded:

Client: ${client}
Project: ${project}
Duration: ${duration} hours
Amount: $${amount}
Date: ${new Date().toDateString()}

View your dashboard: ${SpreadsheetApp.getActiveSpreadsheet().getUrl()}
    `;
    
    // Replace with your email
    GmailApp.sendEmail('<EMAIL>', subject, body);
    
  } catch (error) {
    console.error('Error sending notification: ' + error);
  }
}

// Monthly summary report
function generateMonthlySummary() {
  try {
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var timeSheet = ss.getSheetByName('TimeLog');
    
    var today = new Date();
    var firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    var lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    var data = timeSheet.getDataRange().getValues();
    var monthlyHours = 0;
    var monthlyRevenue = 0;
    var clientSummary = {};
    
    for (var i = 1; i < data.length; i++) {
      var entryDate = new Date(data[i][1]);
      if (entryDate >= firstDay && entryDate <= lastDay) {
        monthlyHours += data[i][6] || 0;
        monthlyRevenue += data[i][8] || 0;
        
        var client = data[i][2];
        if (!clientSummary[client]) {
          clientSummary[client] = {hours: 0, revenue: 0};
        }
        clientSummary[client].hours += data[i][6] || 0;
        clientSummary[client].revenue += data[i][8] || 0;
      }
    }
    
    // Create summary email
    var subject = 'Monthly Summary - ' + (today.getMonth() + 1) + '/' + today.getFullYear();
    var body = `Monthly Business Summary:

Total Hours: ${monthlyHours.toFixed(2)}
Total Revenue: $${monthlyRevenue.toFixed(2)}
Average Rate: $${(monthlyRevenue/monthlyHours).toFixed(2)}/hour

Client Breakdown:
`;
    
    for (var client in clientSummary) {
      body += `${client}: ${clientSummary[client].hours.toFixed(2)} hours, $${clientSummary[client].revenue.toFixed(2)}\n`;
    }
    
    // Send email
    GmailApp.sendEmail('<EMAIL>', subject, body);
    
    console.log('Monthly summary sent');
    
  } catch (error) {
    console.error('Error generating monthly summary: ' + error);
  }
}

// Set up time-driven triggers
function createTriggers() {
  // Delete existing triggers
  var triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(function(trigger) {
    ScriptApp.deleteTrigger(trigger);
  });
  
  // Create new triggers
  ScriptApp.newTrigger('generateMonthlySummary')
    .timeBased()
    .onMonthDay(1)
    .atHour(9)
    .create();
    
  console.log('Triggers created successfully');
}
