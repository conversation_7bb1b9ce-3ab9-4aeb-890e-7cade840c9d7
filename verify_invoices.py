#!/usr/bin/env python3
"""
Invoice Verification Script
Cross-checks that invoice totals match task breakdown totals
"""

import re
from pathlib import Path

def extract_total_from_invoice(file_path):
    """Extract total amount from invoice HTML"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the total amount
    total_match = re.search(r'<strong>\$(\d+\.?\d*)</strong>', content)
    if total_match:
        return float(total_match.group(1))
    return None

def extract_total_from_breakdown(file_path):
    """Extract total amount from task breakdown HTML"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the total value in the stats
    total_match = re.search(r'<div class="stat-value">\$(\d+\.?\d*)</div>\s*<div class="stat-label">Total Value</div>', content)
    if total_match:
        return float(total_match.group(1))
    return None

def extract_hours_from_breakdown(file_path):
    """Extract total hours from task breakdown HTML"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the total hours in the stats
    hours_match = re.search(r'<div class="stat-value">(\d+\.?\d*)</div>\s*<div class="stat-label">Total Hours</div>', content)
    if hours_match:
        return float(hours_match.group(1))
    return None

def main():
    """Verify all invoice and breakdown pairs"""
    print("🔍 Invoice Verification Report")
    print("=" * 50)
    
    invoices_dir = Path("invoices")
    
    # Find all combined invoice files
    invoice_files = list(invoices_dir.glob("*_Complete.html"))
    
    all_verified = True
    total_amount = 0
    total_hours = 0
    
    for invoice_file in sorted(invoice_files):
        invoice_number = invoice_file.stem.replace("_Complete", "")

        print(f"\n📄 {invoice_number}")
        print("-" * 30)

        # Extract amounts from combined file (contains both invoice and breakdown)
        invoice_total = extract_total_from_invoice(invoice_file)
        breakdown_total = extract_total_from_breakdown(invoice_file)
        breakdown_hours = extract_hours_from_breakdown(invoice_file)
        
        if invoice_total is None:
            print("❌ Could not extract total from invoice")
            all_verified = False
            continue
            
        if breakdown_total is None:
            print("❌ Could not extract total from breakdown")
            all_verified = False
            continue
            
        if breakdown_hours is None:
            print("❌ Could not extract hours from breakdown")
            all_verified = False
            continue
        
        # Verify amounts match
        if abs(invoice_total - breakdown_total) < 0.01:  # Allow for rounding
            print(f"✅ Amounts match: ${invoice_total:.2f}")
        else:
            print(f"❌ Amount mismatch!")
            print(f"   Invoice: ${invoice_total:.2f}")
            print(f"   Breakdown: ${breakdown_total:.2f}")
            all_verified = False
        
        # Verify calculation (hours * $80)
        expected_total = breakdown_hours * 80.0
        if abs(breakdown_total - expected_total) < 0.01:
            print(f"✅ Calculation correct: {breakdown_hours}h × $80 = ${expected_total:.2f}")
        else:
            print(f"❌ Calculation error!")
            print(f"   Hours: {breakdown_hours}")
            print(f"   Expected: ${expected_total:.2f}")
            print(f"   Actual: ${breakdown_total:.2f}")
            all_verified = False
        
        # Add to totals
        total_amount += invoice_total
        total_hours += breakdown_hours
        
        print(f"📊 Hours: {breakdown_hours}, Amount: ${invoice_total:.2f}")
    
    print("\n" + "=" * 50)
    print("📈 OVERALL SUMMARY")
    print("=" * 50)
    print(f"Total Hours: {total_hours:.1f}")
    print(f"Total Amount: ${total_amount:.2f}")
    print(f"Average Rate: ${total_amount/total_hours:.2f}/hour" if total_hours > 0 else "No hours recorded")
    
    if all_verified:
        print("\n🎉 ALL INVOICES VERIFIED SUCCESSFULLY!")
        print("✅ All amounts match between invoices and breakdowns")
        print("✅ All calculations are correct")
        print("✅ All files are present and properly formatted")
    else:
        print("\n⚠️  VERIFICATION ISSUES FOUND!")
        print("Please review the errors above and regenerate invoices if needed")
    
    return all_verified

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
