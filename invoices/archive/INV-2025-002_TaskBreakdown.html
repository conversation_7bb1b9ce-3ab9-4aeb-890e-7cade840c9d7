<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Breakdown - Week Ending 07 September 2025</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.5;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 15px;
        }
        
        .report-title {
            font-size: 2.2em;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        
        .period {
            font-size: 1.1em;
            color: #666;
        }
        
        .summary-box {
            background-color: #e3f2fd;
            border-left: 5px solid #2c5aa0;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c5aa0;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        
        .day-section {
            margin: 30px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .day-header {
            background-color: #2c5aa0;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .day-summary {
            background-color: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #ddd;
            font-size: 0.95em;
        }
        
        .task-list {
            background-color: white;
        }
        
        .task-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }
        
        .task-item:last-child {
            border-bottom: none;
        }
        
        .task-time {
            font-weight: bold;
            color: #2c5aa0;
            min-width: 60px;
            font-family: 'Courier New', monospace;
        }
        
        .task-project {
            background-color: #e8f4f8;
            color: #2c5aa0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: bold;
            min-width: 120px;
            text-align: center;
        }
        
        .task-description {
            flex: 1;
            padding-left: 10px;
        }
        
        .footer-note {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="report-title">DETAILED TASK BREAKDOWN</h1>
        <div class="period">Week Ending 07 September 2025 • FSU Client Services</div>
        <div style="margin-top: 10px; font-size: 1.0em; color: #2c5aa0;">
            <strong>Related Invoice:</strong> INV-2025-002
        </div>
    </div>

    <div class="summary-box">
        <h2 style="margin-top: 0; color: #2c5aa0;">Weekly Summary</h2>
        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-value">10.0</div>
                <div class="stat-label">Total Hours</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">6</div>
                <div class="stat-label">Task Entries</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">1</div>
                <div class="stat-label">Active Projects</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">$800.00</div>
                <div class="stat-label">Total Value</div>
            </div>
        </div>
    </div>
    <div class="day-section">
        <div class="day-header">Monday, 01 September 2025</div>
        <div class="day-summary"><strong>Daily Total:</strong> 4.5 hours</div>
        <div class="task-list">
            <div class="task-item">
                <div class="task-time">2.0h</div>
                <div class="task-project">IT Admin</div>
                <div class="task-description">Check MFA settings are correct, set up tasks, send Team draft email</div>
            </div>
            <div class="task-item">
                <div class="task-time">2.5h</div>
                <div class="task-project">IT Admin</div>
                <div class="task-description">Investigated Powerautomate situation and what is needed for MFA and security</div>
            </div>
        </div>
    </div>
    <div class="day-section">
        <div class="day-header">Tuesday, 02 September 2025</div>
        <div class="day-summary"><strong>Daily Total:</strong> 1.0 hours</div>
        <div class="task-list">
            <div class="task-item">
                <div class="task-time">1.0h</div>
                <div class="task-project">IT Admin</div>
                <div class="task-description">Check Teams Premium working correctly adjusted IT notes in SharePoint</div>
            </div>
        </div>
    </div>
    <div class="day-section">
        <div class="day-header">Wednesday, 03 September 2025</div>
        <div class="day-summary"><strong>Daily Total:</strong> 4.5 hours</div>
        <div class="task-list">
            <div class="task-item">
                <div class="task-time">3.0h</div>
                <div class="task-project">IT Admin</div>
                <div class="task-description">Finished the First deliverable Draft, also accessed Bitwarden with CEO recommendation.</div>
            </div>
            <div class="task-item">
                <div class="task-time">1.0h</div>
                <div class="task-project">IT Admin</div>
                <div class="task-description">Formulated recomends of password managers and M365 licencing</div>
            </div>
            <div class="task-item">
                <div class="task-time">0.5h</div>
                <div class="task-project">IT Admin</div>
                <div class="task-description">MFA activated for some users, set up alerts, audit logs. Advised CEO</div>
            </div>
        </div>
    </div>
    <div class="footer-note">
        <p><strong>Report prepared:</strong> 13 September 2025<br>
        <strong>Prepared by:</strong> Carleen Chamberlain<br>
        <strong>Supporting Invoice:</strong> INV-2025-002</p>
        <p><em>All times recorded via automated time tracking system</em></p>
        <p style="margin-top: 10px; font-size: 0.85em; color: #888;">
            <strong>Verification:</strong> Total hours (10.0h) and amount ($800.00)
            match the corresponding invoice for accuracy and transparency.
        </p>
    </div>
</body>
</html>