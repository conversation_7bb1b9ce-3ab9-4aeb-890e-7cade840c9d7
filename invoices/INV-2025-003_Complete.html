<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice INV-2025-003</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.4;
            color: #333;
        }
        
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
        }
        
        .invoice-title {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c5aa0;
            margin: 0;
        }
        
        .invoice-details {
            text-align: right;
            font-size: 0.9em;
        }
        
        .business-details {
            margin-bottom: 30px;
        }
        
        .business-name {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
        }
        
        .client-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
        }
        
        .section-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .invoice-table th {
            background-color: #2c5aa0;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
        }
        
        .invoice-table td {
            padding: 12px;
            border-bottom: 1px solid #ddd;
        }
        
        .invoice-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .total-row {
            background-color: #e3f2fd !important;
            font-weight: bold;
            border-top: 2px solid #2c5aa0;
        }
        
        .amount-cell {
            text-align: right;
            font-family: 'Courier New', monospace;
        }
        
        .total-amount {
            font-size: 1.2em;
            color: #2c5aa0;
        }
        
        .payment-terms {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 30px 0;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 0.9em;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        
        .highlight-box {
            background-color: #e8f4f8;
            border: 2px solid #2c5aa0;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 15px;
        }

        .report-title {
            font-size: 2.2em;
            color: #2c5aa0;
            margin-bottom: 10px;
        }

        .period {
            font-size: 1.1em;
            color: #666;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c5aa0;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .day-section {
            margin: 30px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }

        .day-header {
            background-color: #2c5aa0;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 1.1em;
        }

        .day-summary {
            background-color: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #ddd;
            font-size: 0.95em;
        }

        .task-list {
            background-color: white;
        }

        .task-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-time {
            font-weight: bold;
            color: #2c5aa0;
            min-width: 60px;
            font-family: 'Courier New', monospace;
        }

        .task-project {
            background-color: #e8f4f8;
            color: #2c5aa0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: bold;
            min-width: 120px;
            text-align: center;
        }

        .task-description {
            flex: 1;
            padding-left: 10px;
        }

        .footer-note {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }

        @media print {
            body { margin: 0; }
            .invoice-header { page-break-inside: avoid; }
            .day-section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="invoice-header">
        <div>
            <h1 class="invoice-title">INVOICE</h1>
        </div>
        <div class="invoice-details">
            <strong>Invoice #:</strong> INV-2025-003<br>
            <strong>Date:</strong> 14 September 2025<br>
            <strong>Period:</strong> Week ending 14 September 2025
        </div>
    </div>

    <div class="business-details">
        <div class="business-name">Carleen Chamberlain</div>
        <div>418a Kaitemako Rd</div>
        <div>Welcome Bay, 3175</div>
        <div>Email: <EMAIL></div>
        <div>Phone: 0226051318</div>
    </div>

    <div class="client-section">
        <div class="section-title">Bill To:</div>
        <div><strong>Free Speech Union (NZ) Incorporated</strong></div>
        <div>fsu.nz</div>
    </div>

    <div class="highlight-box">
        <strong>Services Period:</strong> 08 September 2025 – 14 September 2025<br>
        <strong>Total Hours:</strong> 19.5 hours
    </div>

    <table class="invoice-table">
        <thead>
            <tr>
                <th>Description</th>
                <th style="width: 100px;">Hours</th>
                <th style="width: 100px;">Rate</th>
                <th style="width: 120px;">Amount</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>IT Admin<br>
                    <small style="color: #666;">IT Admin services as detailed in attached breakdown</small>
                </td>
                <td class="amount-cell">5.5</td>
                <td class="amount-cell">$80.00</td>
                <td class="amount-cell">$440.00</td>
            </tr>
            <tr>
                <td>IT Continuity<br>
                    <small style="color: #666;">IT Continuity services as detailed in attached breakdown</small>
                </td>
                <td class="amount-cell">14.0</td>
                <td class="amount-cell">$80.00</td>
                <td class="amount-cell">$1120.00</td>
            </tr>
            <tr class="total-row">
                <td colspan="3" style="text-align: right;"><strong>TOTAL (GST Exempt):</strong></td>
                <td class="amount-cell total-amount"><strong>$1560.00</strong></td>
            </tr>
        </tbody>
    </table>

    <div class="payment-terms">
        <div class="section-title">Payment Terms</div>
        <p><strong>Payment Due:</strong> Net 14 days from invoice date</p>
        <p><strong>Payment Method:</strong> Bank transfer preferred</p>
        <p><strong>Bank Details:</strong><br>
        Account Name: Carleen Chamberlain<br>
        Account Number: 04-2021-0224109-11</p>
    </div>

    <div class="highlight-box">
        <strong>Note:</strong> This invoice is GST exempt. Detailed task breakdown follows below.
    </div>

    <!-- PAGE BREAK FOR PRINT -->
    <div style="page-break-before: always; margin-top: 50px;">

        <!-- TASK BREAKDOWN SECTION -->
        <div class="header">
            <h1 class="report-title">DETAILED TASK BREAKDOWN</h1>
            <div style="font-size: 1.0em; color: #2c5aa0; margin-bottom: 5px;">
                <strong>Invoice:</strong> INV-2025-003
            </div>
            <div class="period">Week Ending 14 September 2025 • FSU Client Services</div>
        </div>

        <div class="summary-box">
            <h2 style="margin-top: 0; color: #2c5aa0;">Weekly Summary</h2>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-value">19.5</div>
                    <div class="stat-label">Total Hours</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">9</div>
                    <div class="stat-label">Task Entries</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">2</div>
                    <div class="stat-label">Active Projects</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">$1560.00</div>
                    <div class="stat-label">Total Value</div>
                </div>
            </div>
        </div>
        <div class="day-section">
            <div class="day-header">Monday, 08 September 2025</div>
            <div class="day-summary"><strong>Daily Total:</strong> 0.5 hours</div>
            <div class="task-list">
                <div class="task-item">
                    <div class="task-time">0.5h</div>
                    <div class="task-project">IT Admin</div>
                    <div class="task-description">Checked and verify email content</div>
                </div>
            </div>
        </div>
        <div class="day-section">
            <div class="day-header">Tuesday, 09 September 2025</div>
            <div class="day-summary"><strong>Daily Total:</strong> 6.5 hours</div>
            <div class="task-list">
                <div class="task-item">
                    <div class="task-time">6.5h</div>
                    <div class="task-project">IT Continuity</div>
                    <div class="task-description">Digital ID Meeting setup research structure, workflow with Lucy, reviewed Nucleus, Craft cms, covered logins and workflow with Nathan"</div>
                </div>
            </div>
        </div>
        <div class="day-section">
            <div class="day-header">Wednesday, 10 September 2025</div>
            <div class="day-summary"><strong>Daily Total:</strong> 4.0 hours</div>
            <div class="task-list">
                <div class="task-item">
                    <div class="task-time">1.5h</div>
                    <div class="task-project">IT Admin</div>
                    <div class="task-description">Compiled Research, into templates, set up, notebooklm to share and use</div>
                </div>
                <div class="task-item">
                    <div class="task-time">2.5h</div>
                    <div class="task-project">IT Continuity</div>
                    <div class="task-description">Set up Audit templates for Sign-ins. Setup continuity accounts"</div>
                </div>
            </div>
        </div>
        <div class="day-section">
            <div class="day-header">Thursday, 11 September 2025</div>
            <div class="day-summary"><strong>Daily Total:</strong> 2.5 hours</div>
            <div class="task-list">
                <div class="task-item">
                    <div class="task-time">2.5h</div>
                    <div class="task-project">IT Continuity</div>
                    <div class="task-description">Review current content signin and prep for Off boarding</div>
                </div>
            </div>
        </div>
        <div class="day-section">
            <div class="day-header">Friday, 12 September 2025</div>
            <div class="day-summary"><strong>Daily Total:</strong> 4.0 hours</div>
            <div class="task-list">
                <div class="task-item">
                    <div class="task-time">1.5h</div>
                    <div class="task-project">IT Continuity</div>
                    <div class="task-description">Complete a Social Media access check - created doc, and advised on missing access"</div>
                </div>
                <div class="task-item">
                    <div class="task-time">2.5h</div>
                    <div class="task-project">IT Admin</div>
                    <div class="task-description">Set up Offboarding Doc, Preserved Video footage, received information for how to handle various items from leaving staff. setup admin to ensure a smooth signout."</div>
                </div>
            </div>
        </div>
        <div class="day-section">
            <div class="day-header">Saturday, 13 September 2025</div>
            <div class="day-summary"><strong>Daily Total:</strong> 2.0 hours</div>
            <div class="task-list">
                <div class="task-item">
                    <div class="task-time">1.0h</div>
                    <div class="task-project">IT Continuity</div>
                    <div class="task-description">Checked and secured access and off boarding for FB</div>
                </div>
                <div class="task-item">
                    <div class="task-time">1.0h</div>
                    <div class="task-project">IT Admin</div>
                    <div class="task-description">"Activated OffBoarding in M365 Tenant, confirmed email and inbox forwarding, changed licence.  Created OffBoarding checklists"</div>
                </div>
            </div>
        </div>
        <div class="footer-note">
            <p><strong>Report prepared:</strong> 14 September 2025<br>
            <strong>Prepared by:</strong> Carleen Chamberlain</p>
            <p><em>All times recorded via automated time tracking system</em></p>
        </div>
    </div>

    <div class="footer">
        <p>Thank you for your business! For any queries regarding this invoice, please don't hesitate to contact me.</p>
        <p><em>Professional IT Services • Reliable • Efficient</em></p>
    </div>
</body>
</html>